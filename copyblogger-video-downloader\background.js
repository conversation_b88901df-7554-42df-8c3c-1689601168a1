// Copyblogger Academy Video Downloader - Background Script
console.log('Copyblogger Video Downloader: Background script loaded');

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'downloadVideo') {
    downloadVideo(request.url, request.filename, request.method, sender.tab.id)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch((error) => {
        console.error('Download error:', error);
        sendResponse({ success: false, error: error.message });
      });

    // Return true to indicate we'll send a response asynchronously
    return true;
  }
});

// Download video function
async function downloadVideo(url, filename, method = 'direct', tabId = null) {
  try {
    console.log('Starting download:', { url, filename, method });

    if (method === 'blob') {
      // For blob URLs, try direct download first
      console.log('Attempting direct blob URL download...');

      try {
        const downloadId = await chrome.downloads.download({
          url: url,
          filename: filename,
          saveAs: false
        });

        console.log('Direct blob download started with ID:', downloadId);
        return downloadId;
      } catch (directError) {
        console.log('Direct blob download failed, trying alternative method:', directError.message);

        // Alternative: Execute script in content context to create downloadable blob
        const results = await chrome.scripting.executeScript({
          target: { tabId: tabId },
          func: async (blobUrl, fileName) => {
            try {
              const response = await fetch(blobUrl);
              const blob = await response.blob();

              // Create a download link and trigger it
              const objectUrl = URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = objectUrl;
              link.download = fileName;
              link.style.display = 'none';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // Cleanup after a short delay
              setTimeout(() => {
                URL.revokeObjectURL(objectUrl);
              }, 1000);

              return {
                success: true,
                method: 'content_script_download'
              };
            } catch (error) {
              return {
                success: false,
                error: error.message
              };
            }
          },
          args: [url, filename]
        });

        if (!results[0].result.success) {
          throw new Error(`Content script download failed: ${results[0].result.error}`);
        }

        console.log('Content script download initiated');
        return 'content_script';
      }
    } else {
      // For Rails Active Storage URLs, fetch with credentials
      console.log('Fetching Rails Active Storage URL...');
      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Referer': `https://members.copybloggeracademy.com/`,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
      }

      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      console.log('Created object URL for Rails Active Storage');

      // Start the download
      const downloadId = await chrome.downloads.download({
        url: downloadUrl,
        filename: filename,
        saveAs: false
      });

      console.log('Download started with ID:', downloadId);

      // Monitor download progress and cleanup
      const downloadListener = (downloadDelta) => {
        if (downloadDelta.id === downloadId) {
          if (downloadDelta.state && downloadDelta.state.current === 'complete') {
            console.log('Download completed:', filename);
            URL.revokeObjectURL(downloadUrl);
            chrome.downloads.onChanged.removeListener(downloadListener);
          } else if (downloadDelta.state && downloadDelta.state.current === 'interrupted') {
            console.error('Download interrupted:', filename);
            URL.revokeObjectURL(downloadUrl);
            chrome.downloads.onChanged.removeListener(downloadListener);
          }
        }
      };

      chrome.downloads.onChanged.addListener(downloadListener);

      return downloadId;
    }

  } catch (error) {
    console.error('Download failed:', error);
    throw error;
  }
}

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('Copyblogger Video Downloader installed');
  } else if (details.reason === 'update') {
    console.log('Copyblogger Video Downloader updated');
  }
});

// Handle tab updates to inject content script if needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && 
      tab.url && 
      tab.url.includes('members.copybloggeracademy.com')) {
    console.log('Copyblogger Academy page loaded:', tab.url);
  }
});
