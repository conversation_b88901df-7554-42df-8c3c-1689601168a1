// Copyblogger Academy Video Downloader - Background Script
console.log('Copyblogger Video Downloader: Background script loaded');

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'downloadVideo') {
    downloadVideo(request.url, request.filename)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch((error) => {
        console.error('Download error:', error);
        sendResponse({ success: false, error: error.message });
      });
    
    // Return true to indicate we'll send a response asynchronously
    return true;
  }
});

// Download video function
async function downloadVideo(url, filename) {
  try {
    console.log('Starting download:', { url, filename });
    
    // Get the current tab to access cookies
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    const currentTab = tabs[0];
    
    if (!currentTab || !currentTab.url.includes('copybloggeracademy.com')) {
      throw new Error('Not on Copyblogger Academy page');
    }
    
    // Get cookies for the domain
    const cookies = await chrome.cookies.getAll({
      domain: 'members.copybloggeracademy.com'
    });
    
    // Build cookie string
    const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
    
    // Start the download
    const downloadId = await chrome.downloads.download({
      url: url,
      filename: filename,
      headers: [
        {
          name: 'Cookie',
          value: cookieString
        },
        {
          name: 'Referer',
          value: currentTab.url
        },
        {
          name: 'User-Agent',
          value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      ]
    });
    
    console.log('Download started with ID:', downloadId);
    
    // Monitor download progress
    const downloadListener = (downloadDelta) => {
      if (downloadDelta.id === downloadId) {
        if (downloadDelta.state && downloadDelta.state.current === 'complete') {
          console.log('Download completed:', filename);
          chrome.downloads.onChanged.removeListener(downloadListener);
        } else if (downloadDelta.state && downloadDelta.state.current === 'interrupted') {
          console.error('Download interrupted:', filename);
          chrome.downloads.onChanged.removeListener(downloadListener);
        }
      }
    };
    
    chrome.downloads.onChanged.addListener(downloadListener);
    
    return downloadId;
    
  } catch (error) {
    console.error('Download failed:', error);
    throw error;
  }
}

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('Copyblogger Video Downloader installed');
  } else if (details.reason === 'update') {
    console.log('Copyblogger Video Downloader updated');
  }
});

// Handle tab updates to inject content script if needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && 
      tab.url && 
      tab.url.includes('members.copybloggeracademy.com')) {
    console.log('Copyblogger Academy page loaded:', tab.url);
  }
});
