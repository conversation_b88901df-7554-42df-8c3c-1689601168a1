// Copyblogger Academy Video Downloader - Background Script
console.log('Copyblogger Video Downloader: Background script loaded');

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'downloadVideo') {
    // Only handle Rails Active Storage URLs in background script
    // Blob URLs are handled directly in content script
    if (request.method === 'rails') {
      downloadRailsVideo(request.url, request.filename)
        .then(() => {
          sendResponse({ success: true });
        })
        .catch((error) => {
          console.error('Download error:', error);
          sendResponse({ success: false, error: error.message });
        });
    } else {
      // This shouldn't happen as blob downloads are handled in content script
      sendResponse({ success: false, error: 'Blob downloads should be handled in content script' });
    }

    // Return true to indicate we'll send a response asynchronously
    return true;
  }
});

// Download Rails Active Storage video function
async function downloadRailsVideo(url, filename) {
  try {
    console.log('Starting Rails Active Storage download:', { url, filename });

    // Fetch Rails Active Storage URL with credentials
    const response = await fetch(url, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Referer': `https://members.copybloggeracademy.com/`,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const blob = await response.blob();
    const downloadUrl = URL.createObjectURL(blob);
    console.log('Created object URL for Rails Active Storage');

    // Start the download
    const downloadId = await chrome.downloads.download({
      url: downloadUrl,
      filename: filename,
      saveAs: false
    });

    console.log('Rails download started with ID:', downloadId);

    // Monitor download progress and cleanup
    const downloadListener = (downloadDelta) => {
      if (downloadDelta.id === downloadId) {
        if (downloadDelta.state && downloadDelta.state.current === 'complete') {
          console.log('Rails download completed:', filename);
          URL.revokeObjectURL(downloadUrl);
          chrome.downloads.onChanged.removeListener(downloadListener);
        } else if (downloadDelta.state && downloadDelta.state.current === 'interrupted') {
          console.error('Rails download interrupted:', filename);
          URL.revokeObjectURL(downloadUrl);
          chrome.downloads.onChanged.removeListener(downloadListener);
        }
      }
    };

    chrome.downloads.onChanged.addListener(downloadListener);

    return downloadId;

  } catch (error) {
    console.error('Rails download failed:', error);
    throw error;
  }
}

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('Copyblogger Video Downloader installed');
  } else if (details.reason === 'update') {
    console.log('Copyblogger Video Downloader updated');
  }
});

// Handle tab updates to inject content script if needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && 
      tab.url && 
      tab.url.includes('members.copybloggeracademy.com')) {
    console.log('Copyblogger Academy page loaded:', tab.url);
  }
});
