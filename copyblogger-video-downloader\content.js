// Copyblogger Academy Video Downloader - Content Script
console.log('Copyblogger Video Downloader: Content script loaded');

// Wait for page to fully load
function waitForElement(selector, timeout = 10000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver((mutations, obs) => {
      const element = document.querySelector(selector);
      if (element) {
        obs.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

// Extract video information from the page
function extractVideoInfo() {
  const videos = document.querySelectorAll('video');
  const videoInfo = [];

  videos.forEach((video, index) => {
    const poster = video.getAttribute('poster') || video.getAttribute('data-poster');
    const src = video.src || video.currentSrc;

    let videoUrl = null;
    let filename = 'video.mp4';
    let downloadMethod = 'direct';

    // Priority 1: Use blob URL if available (most reliable for loaded videos)
    if (src && src.startsWith('blob:')) {
      videoUrl = src;
      downloadMethod = 'blob';

      // Extract filename from poster or lesson title
      if (poster) {
        const urlParts = poster.split('/');
        const lastPart = urlParts[urlParts.length - 1];
        if (lastPart.includes('Module') || lastPart.includes('Lesson') || lastPart.includes('.mp4')) {
          filename = decodeURIComponent(lastPart.replace(/\.(png|jpg|jpeg)$/, '.mp4'));
        }
      }

      // Fallback to lesson title
      if (filename === 'video.mp4') {
        const lessonTitle = document.querySelector('h2')?.textContent ||
                           document.querySelector('[data-testid*="lesson"]')?.textContent ||
                           'lesson';
        filename = `${lessonTitle.replace(/[^a-zA-Z0-9\s\-_]/g, '').replace(/\s+/g, '_')}.mp4`;
      }
    }

    // Priority 2: Try Rails Active Storage URL from poster (fallback)
    else if (poster && poster.includes('rails/active_storage')) {
      // Try to construct video URL from poster URL
      videoUrl = poster.replace(/\.(png|jpg|jpeg)$/, '.mp4');
      downloadMethod = 'rails';

      const urlParts = poster.split('/');
      const lastPart = urlParts[urlParts.length - 1];
      if (lastPart.includes('.mp4') || lastPart.includes('Module') || lastPart.includes('Lesson')) {
        filename = decodeURIComponent(lastPart.replace(/\.(png|jpg|jpeg)$/, '.mp4'));
      } else {
        const lessonTitle = document.querySelector('h2')?.textContent || 'lesson';
        filename = `${lessonTitle.replace(/[^a-zA-Z0-9\s\-_]/g, '').replace(/\s+/g, '_')}.mp4`;
      }

      // Ensure URL is absolute
      if (videoUrl.startsWith('/')) {
        videoUrl = window.location.origin + videoUrl;
      }
    }

    // Priority 3: Direct Rails Active Storage URL in src
    else if (src && src.includes('rails/active_storage')) {
      videoUrl = src;
      downloadMethod = 'rails';
      const urlParts = src.split('/');
      const lastPart = urlParts[urlParts.length - 1];
      filename = decodeURIComponent(lastPart);
    }

    if (videoUrl) {
      videoInfo.push({
        element: video,
        url: videoUrl,
        filename: filename,
        method: downloadMethod,
        index: index
      });
    }
  });

  return videoInfo;
}

// Create download button
function createDownloadButton(videoInfo) {
  const button = document.createElement('button');
  button.className = 'copyblogger-download-btn';
  button.innerHTML = `
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
      <polyline points="7,10 12,15 17,10"/>
      <line x1="12" y1="15" x2="12" y2="3"/>
    </svg>
    Download Video
  `;
  
  button.addEventListener('click', () => {
    downloadVideo(videoInfo);
  });

  return button;
}

// Download video function
function downloadVideo(videoInfo) {
  console.log('Downloading video:', videoInfo);

  // Send message to background script to handle download
  chrome.runtime.sendMessage({
    action: 'downloadVideo',
    url: videoInfo.url,
    filename: videoInfo.filename,
    method: videoInfo.method
  }, (response) => {
    if (response && response.success) {
      console.log('Download started successfully');
      // Update button to show success
      const button = document.querySelector('.copyblogger-download-btn');
      if (button) {
        button.innerHTML = '✓ Download Started';
        button.disabled = true;
        setTimeout(() => {
          button.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            Download Video
          `;
          button.disabled = false;
        }, 3000);
      }
    } else {
      console.error('Download failed:', response);
      alert('Download failed. Please try again.');
    }
  });
}

// Add download buttons to video players
function addDownloadButtons() {
  const videoInfos = extractVideoInfo();
  
  videoInfos.forEach((videoInfo) => {
    // Find the video controls container
    const videoContainer = videoInfo.element.closest('.plyr') || 
                          videoInfo.element.closest('[data-testid="media-player"]') ||
                          videoInfo.element.parentElement;
    
    if (videoContainer && !videoContainer.querySelector('.copyblogger-download-btn')) {
      const downloadButton = createDownloadButton(videoInfo);
      
      // Try to find the best place to insert the button
      const controlsContainer = videoContainer.querySelector('.plyr__controls') ||
                               videoContainer.querySelector('[class*="controls"]') ||
                               videoContainer;
      
      // Create a container for our button
      const buttonContainer = document.createElement('div');
      buttonContainer.className = 'copyblogger-download-container';
      buttonContainer.appendChild(downloadButton);
      
      // Insert the button
      if (controlsContainer === videoContainer) {
        controlsContainer.appendChild(buttonContainer);
      } else {
        controlsContainer.insertBefore(buttonContainer, controlsContainer.firstChild);
      }
      
      console.log('Download button added for video:', videoInfo.filename);
    }
  });
}

// Initialize the extension
function init() {
  console.log('Initializing Copyblogger Video Downloader');
  
  // Wait for video elements to load
  waitForElement('video').then(() => {
    console.log('Video element found, adding download buttons');
    addDownloadButtons();
    
    // Also watch for dynamically loaded videos
    const observer = new MutationObserver((mutations) => {
      let shouldCheck = false;
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1 && (node.tagName === 'VIDEO' || node.querySelector('video'))) {
              shouldCheck = true;
            }
          });
        }
      });
      
      if (shouldCheck) {
        setTimeout(addDownloadButtons, 1000);
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
  }).catch((error) => {
    console.log('No video elements found:', error.message);
  });
}

// Handle messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getVideoCount') {
    const videoInfos = extractVideoInfo();
    sendResponse({ count: videoInfos.length });
  }
});

// Start when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}
