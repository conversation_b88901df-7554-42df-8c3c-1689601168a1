# Changelog

All notable changes to the Copyblogger Academy Video Downloader extension will be documented in this file.

## [1.1.0] - 2024-01-XX - NETWORK ERROR FIXED 🎉

### 🔧 Fixed
- **MAJOR**: Fixed "Network error" when downloading videos
- **MAJOR**: Blob URL downloads now work correctly
- **MAJOR**: Resolved Chrome Downloads API security restrictions

### 🚀 Added
- **NEW**: Dual download strategy (blob URLs + Rails Active Storage)
- **NEW**: Content script handles blob downloads directly
- **NEW**: Visual download states (⏳ Downloading, ✅ Success, ❌ Error)
- **NEW**: Better error handling with user feedback
- **NEW**: Automatic fallback system for maximum compatibility

### 🔄 Changed
- **BREAKING**: Blob URLs now downloaded in content script context
- **IMPROVED**: Background script only handles Rails Active Storage URLs
- **IMPROVED**: Better separation of concerns between content and background scripts
- **IMPROVED**: More reliable download process

### 🛠️ Technical Details
- Content script now uses `fetch()` to access blob URLs directly
- Creates download links and triggers them in the correct context
- Background script simplified to handle only Rails Active Storage URLs
- Added proper object URL cleanup to prevent memory leaks

### 📋 Migration Notes
- No user action required - extension will automatically use new download method
- Existing installations should reload the extension for best results
- All previous functionality remains intact with improved reliability

---

## [1.0.0] - 2024-01-XX - Initial Release

### 🚀 Added
- **NEW**: Basic video detection on Copyblogger Academy lesson pages
- **NEW**: Integrated download button in video player
- **NEW**: Rails Active Storage URL support
- **NEW**: Automatic filename extraction from lesson titles
- **NEW**: Chrome Extension Manifest V3 support
- **NEW**: Session cookie handling for authenticated downloads

### 🔧 Features
- Detects video elements automatically
- Extracts video URLs from poster attributes
- Downloads videos with proper filenames
- Handles authentication via session cookies
- Clean UI integration with existing video player

### 📦 Initial Components
- `manifest.json` - Extension configuration
- `content.js` - Video detection and UI injection
- `background.js` - Download handling
- `popup.html/js` - Extension popup interface
- `styles.css` - Download button styling
- Extension icons and documentation

### 🎯 Supported Features
- Video detection on lesson pages
- Rails Active Storage URL parsing
- Authenticated downloads
- Automatic file naming
- Download progress monitoring

---

## Known Issues (Resolved in v1.1.0)

### ❌ v1.0.0 Issues (FIXED)
- ~~Network error when downloading blob URLs~~ ✅ **FIXED in v1.1.0**
- ~~Chrome Downloads API security restrictions~~ ✅ **FIXED in v1.1.0**
- ~~Blob URL access from background script~~ ✅ **FIXED in v1.1.0**

### 🔮 Future Enhancements
- [ ] Batch download multiple videos
- [ ] Download quality selection
- [ ] Progress bar for large downloads
- [ ] Download history management
- [ ] Custom download location selection
