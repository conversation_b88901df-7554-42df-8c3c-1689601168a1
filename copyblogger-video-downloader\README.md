# Copyblogger Academy Video Downloader

Ekstensi Chrome untuk mendownload video dari halaman lesson Copyblogger Academy.

## Fitur

- ✅ Deteksi otomatis video di halaman Copyblogger Academy
- ✅ Tombol download yang terintegrasi dengan video player
- ✅ Download video dengan nama file yang sesuai
- ✅ Menangani autentikasi dan cookies secara otomatis
- ✅ Interface yang user-friendly

## Instalasi

### Metode 1: Load Unpacked Extension (Recommended)

1. **Download atau clone repository ini**
   ```bash
   git clone [repository-url]
   # atau download sebagai ZIP dan extract
   ```

2. **Buka Chrome Extension Manager**
   - Buka Chrome browser
   - Ketik `chrome://extensions/` di address bar
   - Atau buka menu Chrome → More Tools → Extensions

3. **Enable Developer Mode**
   - Toggle "Developer mode" di pojok kanan atas

4. **Load Extension**
   - Klik "Load unpacked"
   - Pilih folder `copyblogger-video-downloader`
   - Extension akan muncul di daftar extensions

5. **Pin Extension (Optional)**
   - Klik icon puzzle di toolbar Chrome
   - Klik pin icon di samping "Copyblogger Academy Video Downloader"

## Cara Penggunaan

1. **Login ke Copyblogger Academy**
   - Buka https://members.copybloggeracademy.com
   - Login dengan akun Anda

2. **Buka Halaman Lesson**
   - Navigate ke lesson yang memiliki video
   - Contoh: https://members.copybloggeracademy.com/c/high-caliber-copywriting/sections/567201/lessons/2135473

3. **Download Video**
   - Tunggu video player dimuat
   - Tombol "Download Video" akan muncul di dekat video player
   - Klik tombol tersebut untuk memulai download
   - Video akan disimpan ke folder Downloads default browser

## Troubleshooting

### Tombol Download Tidak Muncul

1. **Refresh halaman** - Kadang extension perlu waktu untuk load
2. **Check console** - Buka Developer Tools (F12) dan lihat console untuk error
3. **Pastikan sudah login** - Extension memerlukan session yang valid
4. **Coba disable/enable extension** - Di chrome://extensions/

### Download Gagal

1. **Check permissions** - Pastikan extension memiliki permission untuk downloads
2. **Check cookies** - Pastikan masih login ke Copyblogger Academy
3. **Try different browser** - Kadang ada issue dengan Chrome settings
4. **Check network** - Pastikan koneksi internet stabil

### Video Tidak Terdeteksi

1. **Tunggu video load** - Biarkan video player selesai loading
2. **Check video format** - Extension hanya support video dengan Rails Active Storage
3. **Refresh page** - Coba refresh dan tunggu beberapa detik

## Technical Details

### Cara Kerja

1. **Content Script** (`content.js`) berjalan di halaman Copyblogger Academy
2. **Deteksi Video** - Mencari elemen `<video>` dan URL Rails Active Storage
3. **UI Integration** - Menambahkan tombol download ke video player
4. **Background Script** (`background.js`) menangani download dengan cookies
5. **Chrome Downloads API** - Menggunakan API native Chrome untuk download

### Supported URLs

Extension bekerja dengan URL yang mengandung:
- `members.copybloggeracademy.com`
- Video dengan Rails Active Storage URLs
- Format: `/rails/active_storage/representations/redirect/.../*.mp4`

### Permissions

Extension memerlukan permissions berikut:
- `activeTab` - Akses ke tab aktif
- `downloads` - Download files
- `storage` - Simpan settings
- `cookies` - Akses cookies untuk autentikasi
- `host_permissions` - Akses ke domain Copyblogger Academy

## Development

### File Structure

```
copyblogger-video-downloader/
├── manifest.json          # Extension configuration
├── content.js             # Main content script
├── background.js          # Service worker
├── popup.html            # Extension popup UI
├── popup.js              # Popup functionality
├── styles.css            # Styling for download button
└── README.md             # This file
```

### Testing

1. Load extension in developer mode
2. Navigate to Copyblogger Academy lesson
3. Check browser console for logs
4. Test download functionality

### Contributing

1. Fork repository
2. Make changes
3. Test thoroughly
4. Submit pull request

## Disclaimer

- Extension ini dibuat untuk tujuan edukasi dan personal use
- Pastikan Anda memiliki akses legal ke konten yang didownload
- Respect terms of service dari Copyblogger Academy
- Gunakan dengan bijak dan bertanggung jawab

## Support

Jika mengalami masalah:
1. Check troubleshooting section di atas
2. Open issue di repository
3. Sertakan informasi browser dan error message

## Version History

- **v1.0** - Initial release
  - Basic video detection and download
  - Rails Active Storage support
  - Chrome extension integration

## Quick Start

1. **Download extension files** ke folder lokal
2. **Buka Chrome** → `chrome://extensions/`
3. **Enable Developer mode** (toggle di kanan atas)
4. **Click "Load unpacked"** → pilih folder `copyblogger-video-downloader`
5. **Login ke Copyblogger Academy** dan buka lesson dengan video
6. **Klik tombol "Download Video"** yang muncul di video player

## Notes

- Extension ini memerlukan login session yang valid ke Copyblogger Academy
- Video akan didownload ke folder Downloads default browser
- Pastikan popup blocker tidak menghalangi download
- Extension hanya bekerja di halaman lesson Copyblogger Academy yang memiliki video
