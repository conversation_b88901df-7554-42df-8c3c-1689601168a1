/* Copyblogger Academy Video Downloader - Styles */

.copyblogger-download-container {
  position: relative;
  z-index: 9999;
  margin: 10px;
}

.copyblogger-download-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #506CF0;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(80, 108, 240, 0.2);
}

.copyblogger-download-btn:hover {
  background: #4056d6;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(80, 108, 240, 0.3);
}

.copyblogger-download-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(80, 108, 240, 0.2);
}

.copyblogger-download-btn:disabled {
  background: #22BB22;
  cursor: not-allowed;
  transform: none;
}

.copyblogger-download-btn svg {
  flex-shrink: 0;
}

/* Position the button near video controls */
.plyr .copyblogger-download-container {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}

/* Alternative positioning for different video player layouts */
[data-testid="media-player"] .copyblogger-download-container {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
}

/* Responsive design */
@media (max-width: 768px) {
  .copyblogger-download-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .copyblogger-download-container {
    margin: 5px;
  }
  
  .plyr .copyblogger-download-container,
  [data-testid="media-player"] .copyblogger-download-container {
    top: 5px;
    right: 5px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .copyblogger-download-btn {
    background: #4056d6;
  }
  
  .copyblogger-download-btn:hover {
    background: #3448c5;
  }
}
