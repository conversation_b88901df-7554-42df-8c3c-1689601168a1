// Copyblogger Academy Video Downloader - Popup Script
document.addEventListener('DOMContentLoaded', async () => {
  const currentPageElement = document.getElementById('currentPage');
  const videoCountElement = document.getElementById('videoCount');
  const refreshBtn = document.getElementById('refreshBtn');
  
  // Get current tab information
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    const currentTab = tabs[0];
    
    if (currentTab) {
      // Update current page status
      if (currentTab.url.includes('members.copybloggeracademy.com')) {
        currentPageElement.textContent = 'Copyblogger Academy';
        currentPageElement.style.color = '#22BB22';
        
        // Try to get video count from content script
        try {
          const response = await chrome.tabs.sendMessage(currentTab.id, { action: 'getVideoCount' });
          if (response && typeof response.count === 'number') {
            videoCountElement.textContent = response.count;
            videoCountElement.style.color = response.count > 0 ? '#22BB22' : '#666';
          } else {
            videoCountElement.textContent = 'Unknown';
          }
        } catch (error) {
          console.log('Could not get video count:', error);
          videoCountElement.textContent = 'Unknown';
        }
        
      } else {
        currentPageElement.textContent = 'Not Copyblogger Academy';
        currentPageElement.style.color = '#ff6b6b';
        videoCountElement.textContent = 'N/A';
      }
    }
  } catch (error) {
    console.error('Error getting tab info:', error);
    currentPageElement.textContent = 'Error';
    currentPageElement.style.color = '#ff6b6b';
  }
  
  // Refresh button functionality
  refreshBtn.addEventListener('click', async () => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const currentTab = tabs[0];
      
      if (currentTab) {
        await chrome.tabs.reload(currentTab.id);
        window.close();
      }
    } catch (error) {
      console.error('Error refreshing page:', error);
    }
  });
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'updateVideoCount') {
    const videoCountElement = document.getElementById('videoCount');
    if (videoCountElement) {
      videoCountElement.textContent = request.count;
      videoCountElement.style.color = request.count > 0 ? '#22BB22' : '#666';
    }
  }
});
