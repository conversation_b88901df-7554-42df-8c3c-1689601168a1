<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .logo {
      width: 48px;
      height: 48px;
      background: #506CF0;
      border-radius: 12px;
      margin: 0 auto 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
    }
    
    h1 {
      font-size: 18px;
      margin: 0 0 5px 0;
      color: #333;
    }
    
    .subtitle {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
    
    .status {
      background: #f5f5f5;
      border-radius: 8px;
      padding: 15px;
      margin: 20px 0;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .status-item:last-child {
      margin-bottom: 0;
    }
    
    .status-label {
      font-size: 14px;
      color: #666;
    }
    
    .status-value {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
    
    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-left: 8px;
    }
    
    .status-active {
      background: #22BB22;
    }
    
    .status-inactive {
      background: #ccc;
    }
    
    .instructions {
      font-size: 13px;
      color: #666;
      line-height: 1.4;
      margin-top: 15px;
    }
    
    .button {
      width: 100%;
      padding: 10px;
      background: #506CF0;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      cursor: pointer;
      margin-top: 15px;
    }
    
    .button:hover {
      background: #4056d6;
    }
    
    .button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">📹</div>
    <h1>Video Downloader</h1>
    <p class="subtitle">for Copyblogger Academy</p>
  </div>
  
  <div class="status">
    <div class="status-item">
      <span class="status-label">Current Page:</span>
      <span class="status-value" id="currentPage">Loading...</span>
    </div>
    <div class="status-item">
      <span class="status-label">Videos Found:</span>
      <span class="status-value" id="videoCount">-</span>
    </div>
    <div class="status-item">
      <span class="status-label">Extension Status:</span>
      <span class="status-value">
        Active
        <span class="status-indicator status-active"></span>
      </span>
    </div>
  </div>
  
  <div class="instructions">
    <strong>How to use:</strong><br>
    1. Navigate to a Copyblogger Academy lesson page<br>
    2. Look for the download button near the video player<br>
    3. Click the download button to save the video
  </div>
  
  <button class="button" id="refreshBtn">Refresh Page</button>
  
  <script src="popup.js"></script>
</body>
</html>
